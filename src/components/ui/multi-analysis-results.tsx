import React from 'react';
import { Card } from './card';
import { Badge } from './badge';
import AttractivenessPieChart from './pie-chart';

interface Factor {
  name: string;
  score: number;
  description: string;
  recommendation: string;
}

interface AnalysisItem {
  type: string;
  overall_score?: number;
  factors?: Factor[];
  analysis_text?: string;
  overall_recommendations?: string;
  shape?: string;
  confidence_score?: number;
  measurements?: any;
  styling_tips?: any;
  celebrity_examples?: string[];
  description?: string;
}

interface MultiAnalysisResult {
  analyses: AnalysisItem[];
}

interface AttractivenessResult {
  overall_score: number;
  factors: Factor[];
  analysis_text: string;
  overall_recommendations: string;
}

interface MultiAnalysisResultsProps {
  data: AttractivenessResult | MultiAnalysisResult | null;
}

const getAnalysisTitle = (type: string): string => {
  const titles: { [key: string]: string } = {
    'attractive-score': 'Attractiveness Analysis',
    'face-shape-detector': 'Face Shape Analysis',
    'golden-ratio-face': 'Golden Ratio Analysis',
    'smile-rating': 'Smile Analysis'
  };
  return titles[type] || type;
};

const FaceShapeAnalysis = ({ analysis }: { analysis: AnalysisItem }) => (
  <Card className="p-6">
    <h3 className="text-2xl font-bold mb-4">Face Shape Analysis</h3>
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <span className="text-lg font-semibold">Detected Shape:</span>
        <Badge variant="secondary" className="text-lg px-3 py-1">
          {analysis.shape}
        </Badge>
      </div>
      {analysis.confidence_score && (
        <div className="flex items-center gap-2">
          <span className="text-lg font-semibold">Confidence:</span>
          <span className="text-lg">{(analysis.confidence_score * 100).toFixed(1)}%</span>
        </div>
      )}
      {analysis.description && (
        <div>
          <h4 className="font-semibold mb-2">Description:</h4>
          <p className="text-muted-foreground">{analysis.description}</p>
        </div>
      )}
      {analysis.styling_tips && (
        <div>
          <h4 className="font-semibold mb-2">Styling Tips:</h4>
          <div className="space-y-2">
            {Object.entries(analysis.styling_tips).map(([key, value]) => (
              <div key={key}>
                <span className="font-medium capitalize">{key.replace('_', ' ')}:</span>
                <span className="ml-2 text-muted-foreground">{value as string}</span>
              </div>
            ))}
          </div>
        </div>
      )}
      {analysis.celebrity_examples && analysis.celebrity_examples.length > 0 && (
        <div>
          <h4 className="font-semibold mb-2">Celebrity Examples:</h4>
          <div className="flex flex-wrap gap-2">
            {analysis.celebrity_examples.map((celebrity, index) => (
              <Badge key={index} variant="outline">{celebrity}</Badge>
            ))}
          </div>
        </div>
      )}
    </div>
  </Card>
);

const GoldenRatioAnalysis = ({ analysis }: { analysis: AnalysisItem }) => (
  <Card className="p-6">
    <h3 className="text-2xl font-bold mb-4">Golden Ratio Analysis</h3>
    <div className="space-y-4">
      {analysis.overall_score && (
        <div className="text-center">
          <div className="text-4xl font-bold text-primary mb-2">
            {analysis.overall_score.toFixed(1)}/10
          </div>
          <p className="text-muted-foreground">Golden Ratio Score</p>
        </div>
      )}
      {analysis.analysis_text && (
        <div>
          <h4 className="font-semibold mb-2">Analysis:</h4>
          <p className="text-muted-foreground italic">"{analysis.analysis_text}"</p>
        </div>
      )}
      {analysis.measurements && (
        <div>
          <h4 className="font-semibold mb-2">Measurements:</h4>
          <div className="grid grid-cols-2 gap-4">
            {Object.entries(analysis.measurements).map(([key, value]) => (
              <div key={key} className="flex justify-between">
                <span className="capitalize">{key.replace('_', ' ')}:</span>
                <span className="font-medium">{value as string}</span>
              </div>
            ))}
          </div>
        </div>
      )}
      {analysis.overall_recommendations && (
        <div>
          <h4 className="font-semibold mb-2">Recommendations:</h4>
          <p className="text-muted-foreground">{analysis.overall_recommendations}</p>
        </div>
      )}
    </div>
  </Card>
);

export default function MultiAnalysisResults({ data }: MultiAnalysisResultsProps) {
  if (!data) return null;

  // Handle single analysis (backward compatibility)
  const isSingleAnalysis = 'factors' in data;
  if (isSingleAnalysis) {
    return <AttractivenessPieChart data={data} />;
  }

  // Handle multi-analysis
  const multiData = data as MultiAnalysisResult;
  const analyses = multiData.analyses;

  return (
    <div className="space-y-8">
      {analyses.map((analysis, index) => {
        switch (analysis.type) {
          case 'attractive-score':
            // Convert to single analysis format for the pie chart
            const attractiveData: AttractivenessResult = {
              overall_score: analysis.overall_score || 0,
              factors: analysis.factors || [],
              analysis_text: analysis.analysis_text || '',
              overall_recommendations: analysis.overall_recommendations || ''
            };
            return (
              <div key={index}>
                <AttractivenessPieChart data={attractiveData} />
              </div>
            );
          
          case 'face-shape-detector':
            return <FaceShapeAnalysis key={index} analysis={analysis} />;
          
          case 'golden-ratio-face':
            return <GoldenRatioAnalysis key={index} analysis={analysis} />;
          
          default:
            return (
              <Card key={index} className="p-6">
                <h3 className="text-2xl font-bold mb-4">{getAnalysisTitle(analysis.type)}</h3>
                <div className="space-y-4">
                  {analysis.overall_score && (
                    <div className="text-center">
                      <div className="text-4xl font-bold text-primary mb-2">
                        {analysis.overall_score.toFixed(1)}/10
                      </div>
                    </div>
                  )}
                  {analysis.analysis_text && (
                    <p className="text-muted-foreground italic">"{analysis.analysis_text}"</p>
                  )}
                  {analysis.overall_recommendations && (
                    <div>
                      <h4 className="font-semibold mb-2">Recommendations:</h4>
                      <p className="text-muted-foreground">{analysis.overall_recommendations}</p>
                    </div>
                  )}
                </div>
              </Card>
            );
        }
      })}
    </div>
  );
}
