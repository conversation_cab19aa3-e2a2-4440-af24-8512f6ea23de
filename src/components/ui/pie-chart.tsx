import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON> } from 'recharts';

interface Factor {
  name: string;
  score: number;
  description: string;
  recommendation: string;
}

interface AttractivenessResult {
  overall_score: number;
  factors: Factor[];
  analysis_text: string;
  overall_recommendations: string;
}

interface AnalysisItem {
  type: string;
  overall_score?: number;
  factors?: Factor[];
  analysis_text?: string;
  overall_recommendations?: string;
  shape?: string;
  confidence_score?: number;
  measurements?: any;
  styling_tips?: any;
  celebrity_examples?: string[];
  description?: string;
}

interface MultiAnalysisResult {
  analyses: AnalysisItem[];
}

interface PieChartComponentProps {
  data: AttractivenessResult | MultiAnalysisResult | null;
}

const COLORS = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A', '#98D8C8', '#F7DC6F', '#BB8FCE'];

export default function AttractivenessPieChart({ data }: PieChartComponentProps) {
  if (!data) return null;

  // Handle both single and multi-analysis formats
  const isSingleAnalysis = 'factors' in data;
  let currentData: AttractivenessResult;

  if (isSingleAnalysis) {
    currentData = data as AttractivenessResult;
  } else {
    // For multi-analysis, use the first analysis result (attractive-score) for the pie chart
    const multiData = data as MultiAnalysisResult;
    const attractiveScoreAnalysis = multiData.analyses.find(analysis => analysis.type === 'attractive-score');

    if (attractiveScoreAnalysis && attractiveScoreAnalysis.factors) {
      // Convert to AttractivenessResult format
      currentData = {
        overall_score: attractiveScoreAnalysis.overall_score || 0,
        factors: attractiveScoreAnalysis.factors,
        analysis_text: attractiveScoreAnalysis.analysis_text || '',
        overall_recommendations: attractiveScoreAnalysis.overall_recommendations || ''
      };
    } else {
      return <p>No attractiveness analysis data available</p>;
    }
  }

  // Convert factors to pie chart data (scores out of 100)
  const pieData = (currentData.factors || []).map((factor: Factor, index: number) => ({
    name: factor.name,
    value: Math.round(factor.score * 10), // Convert to 0-100 scale
    score: factor.score,
    description: factor.description,
    recommendation: factor.recommendation,
    color: COLORS[index % COLORS.length]
  }));

  // Add overall score as a separate chunk (showing the remaining to 100)
  const totalScore = pieData.reduce((sum: number, item: any) => sum + item.value, 0);
  const remainingScore = 100 - totalScore;
  if (remainingScore > 0) {
    pieData.push({
      name: 'Overall Base',
      value: remainingScore,
      score: 0,
      description: '',
      recommendation: '',
      color: '#E8E8E8'
    });
  }

  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: any) => {
    if (percent < 0.05) return null; // Don't show labels for very small slices

    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text x={x} y={y} fill="white" textAnchor={x > cx ? 'start' : 'end'} dominantBaseline="central" fontSize={12} fontWeight="bold">
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

    return (
    <div className="w-full max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="mb-6 text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-2">
          Your Attractiveness Score: {currentData.overall_score}/10
        </h2>
        <div className="text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-pink-500 to-purple-600">
          {Math.round(currentData.overall_score * 10)}%
        </div>
        <p className="text-lg text-gray-600 mt-4 max-w-2xl mx-auto">
          {currentData.analysis_text}
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-8">
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={pieData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={renderCustomizedLabel}
                outerRadius={100}
                fill="#8884d8"
                dataKey="value"
              >
                {pieData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Legend
                verticalAlign="bottom"
                height={36}
                formatter={(value, entry: any) => (
                  <span style={{ color: entry.color }}>
                    {value} ({entry.payload.score.toFixed(1)})
                  </span>
                )}
              />
            </PieChart>
          </ResponsiveContainer>
        </div>

        <div className="space-y-4">
          <h3 className="text-xl font-semibold text-gray-900">Detailed Analysis</h3>
          {currentData.factors.map((factor: Factor, index: number) => (
            <div key={factor.name} className="bg-gray-50 p-4 rounded-lg">
              <div className="flex justify-between items-start mb-2">
                <h4 className="font-medium text-gray-900">{factor.name}</h4>
                <span className="text-sm font-semibold px-2 py-1 rounded"
                      style={{ backgroundColor: COLORS[index], color: 'white' }}>
                  {factor.score.toFixed(1)}/10
                </span>
              </div>
              <p className="text-sm text-gray-600 mb-2">{factor.description}</p>
              <p className="text-sm text-blue-600 italic">{factor.recommendation}</p>
            </div>
          ))}

          <div className="mt-6 p-4 bg-gradient-to-r from-pink-50 to-purple-50 rounded-lg">
            <h4 className="font-semibold text-gray-900 mb-2">Overall Recommendations</h4>
            <p className="text-gray-700">{currentData.overall_recommendations}</p>
          </div>
        </div>
      </div>
    </div>
  );
}
