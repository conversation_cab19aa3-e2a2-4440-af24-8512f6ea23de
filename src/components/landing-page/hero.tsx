'use client';

import Image from "next/image";
import { useState, useRef } from "react";
import { Button } from "../ui/button";
import { Checkbox } from "../ui/checkbox";
import { Crown, Upload, Camera } from "lucide-react";
import AttractivenessPieChart from "../ui/pie-chart";
import { toast } from "sonner";

interface AttractivenessResult {
  overall_score: number;
  factors: any[];
  analysis_text: string;
  overall_recommendations: string;
}

interface MultiAnalysisResult {
  analyses: {
    type: string;
    overall_score?: number;
    factors?: any[];
    analysis_text?: string;
    overall_recommendations?: string;
    shape?: string;
    confidence_score?: number;
    measurements?: any;
    styling_tips?: any;
    celebrity_examples?: string[];
    description?: string;
  }[];
}

export default function Hero() {
  const [selectedAnalysis, setSelectedAnalysis] = useState<string[]>(["attractive-score"]);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [result, setResult] = useState<AttractivenessResult | null>(null);
  const [dragActive, setDragActive] = useState(false);
  const [captureMode, setCaptureMode] = useState<'upload' | 'camera'>('upload');
  const [showCamera, setShowCamera] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const analysisOptions = [
    {
      id: "attractive-score",
      label: "How attractive am I",
      description: "Complete facial attractiveness analysis with detailed scoring"
    },
    {
      id: "face-shape-detector",
      label: "Face shape detector",
      description: "Identify your face shape and get personalized styling tips"
    },
    {
      id: "golden-ratio-face",
      label: "Golden ratio face",
      description: "Advanced analysis based on classical beauty proportions"
    }
  ];

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0];
      if (file.type.startsWith('image/')) {
        setSelectedFile(file);
      } else {
        toast.error("Please select an image file");
      }
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setSelectedFile(e.target.files[0]);
    }
  };

  const handleAnalysis = async () => {
    if (!selectedFile) {
      toast.error("Please select an image first");
      return;
    }

    setIsAnalyzing(true);
    setResult(null);

    try {
      const formData = new FormData();
      formData.append('image', selectedFile);
      formData.append('analysisType', JSON.stringify(selectedAnalysis));

      const response = await fetch('/api/analyze-image', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();

      if (response.ok) {
        setResult(data);
        toast.success("Analysis complete!");
      } else {
        toast.error(data.error || "Analysis failed");
      }
    } catch (error) {
      console.error('Analysis error:', error);
      toast.error("Failed to analyze image. Please try again.");
    } finally {
      setIsAnalyzing(false);
    }
  };

  const startCamera = async () => {
    setShowCamera(true);
    setCaptureMode('camera');
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: 'user' } // Use front camera on mobile
      });
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }
    } catch (error) {
      console.error('Error accessing camera:', error);
      toast.error('Unable to access camera. Please make sure you have granted camera permissions.');
      setShowCamera(false);
    }
  };

  const capturePhoto = () => {
    if (videoRef.current && canvasRef.current) {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      const context = canvas.getContext('2d');

      if (context) {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        context.drawImage(video, 0, 0, canvas.width, canvas.height);

        canvas.toBlob((blob) => {
          if (blob) {
            const file = new File([blob], 'captured-photo.jpg', { type: 'image/jpeg' });
            setSelectedFile(file);

            // Stop camera stream
            if (video.srcObject) {
              const stream = video.srcObject as MediaStream;
              stream.getTracks().forEach(track => track.stop());
            }
            setShowCamera(false);
          }
        }, 'image/jpeg', 0.95);
      }
    }
  };

  const stopCamera = () => {
    if (videoRef.current && videoRef.current.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream.getTracks().forEach(track => track.stop());
    }
    setShowCamera(false);
    setCaptureMode('upload');
  };

  const removeFile = () => {
    setSelectedFile(null);
    setResult(null);
    setCaptureMode('upload');
    setShowCamera(false);
  };

  return (
    <section className="relative min-h-screen flex items-center justify-center pt-16">
      <div className="container grid grid-cols-1 lg:grid-cols-2 gap-8 items-center max-w-7xl mx-auto px-4">
        {!result ? (
          <>
            <div className="flex flex-col gap-6 text-left">
              <div className="space-y-4">
                <div className="inline-block rounded-lg bg-muted px-3 py-1 text-sm">
                  ✨ AI-Powered Analysis
                </div>
                <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl hero-title">
                  <span className="bg-gradient-to-r from-violet-500 via-pink-500 to-yellow-500 bg-clip-text text-transparent animate-gradient">
                  How Attractive Am I?
                  </span>
                </h1>
                <p className="text-lg md:text-xl text-muted-foreground">
                  Get instant, AI-powered insights into your visual appeal and facial features
                </p>
              </div>

              {/* Analysis Options */}
              <div className="space-y-3">
                <h3 className="text-lg font-semibold">Choose your analysis:</h3>
                {analysisOptions.map((option) => (
                  <div key={option.id} className="flex items-start space-x-3 p-3 border rounded-lg hover:border-primary/50 transition-colors">
                    <Checkbox
                      id={option.id}
                      checked={selectedAnalysis.includes(option.id)}
                      onCheckedChange={(checked: boolean) => {
                        if (checked) {
                          setSelectedAnalysis(prev => [...prev, option.id]);
                        } else {
                          setSelectedAnalysis(prev => prev.filter(id => id !== option.id));
                        }
                      }}
                      className="mt-1"
                    />
                    <div className="flex-1">
                      <label htmlFor={option.id} className="flex items-center gap-2 cursor-pointer">
                        <span className="font-medium">{option.label}</span>
                      </label>
                      <p className="text-sm text-muted-foreground mt-1">{option.description}</p>
                    </div>
                  </div>
                ))}
              </div>

              <Button onClick={handleAnalysis} disabled={!selectedFile || isAnalyzing} size="lg" className="w-full sm:w-auto">
                {isAnalyzing ? "Analyzing..." : "Analyze My Face"}
              </Button>
            </div>

            <div className="flex flex-col gap-4">
              {/* Image Selection Area */}
              <div className="space-y-4">
                {/* Mode Toggle */}
                <div className="flex gap-2 justify-center">
                  <Button
                    variant={captureMode === 'upload' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setCaptureMode('upload')}
                  >
                    <Upload className="mr-2 h-4 w-4" />
                    Upload Photo
                  </Button>
                  <Button
                    variant={captureMode === 'camera' ? 'default' : 'outline'}
                    size="sm"
                    onClick={startCamera}
                  >
                    <Camera className="mr-2 h-4 w-4" />
                    Snap Photo
                  </Button>
                </div>

                {/* Upload/Camera Area */}
                <div className="relative">
                  {showCamera && captureMode === 'camera' ? (
                    <div className="border-2 border-dashed border-violet-400 rounded-xl p-4 bg-violet-50">
                      <div className="text-center space-y-4">
                        <video
                          ref={videoRef}
                          autoPlay
                          playsInline
                          className="w-full h-64 object-cover rounded-lg bg-gray-200"
                        />
                        <canvas ref={canvasRef} className="hidden" />
                        <div className="flex gap-2 justify-center">
                          <Button onClick={capturePhoto}>
                            <Camera className="mr-2 h-4 w-4" />
                            Take Photo
                          </Button>
                          <Button variant="outline" onClick={stopCamera}>
                            Cancel
                          </Button>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className={`border-2 border-dashed border-muted-foreground/25 rounded-xl p-8 transition-all duration-300 shadow-sm hover:shadow-md ${
                      dragActive ? 'border-violet-400 bg-violet-50 scale-105' : 'border-gray-300 hover:border-gray-400'
                    }`}
                         onDragEnter={handleDrag}
                         onDragLeave={handleDrag}
                         onDragOver={handleDrag}
                         onDrop={handleDrop}>
                      {!selectedFile ? (
                        <div className="text-center">
                          <Upload className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                          <p className="text-lg font-medium mb-2">
                            {captureMode === 'upload' ? 'Upload your photo' : 'Select a photo'}
                          </p>
                          <p className="text-muted-foreground mb-4">
                            Drag and drop or click to select an image
                          </p>
                          {captureMode === 'upload' && (
                            <>
                              <input
                                type="file"
                                accept="image/*"
                                onChange={handleFileSelect}
                                className="hidden"
                                id="file-upload"
                              />
                              <label htmlFor="file-upload" className="cursor-pointer">
                                <Button variant="outline" className="mx-auto">
                                  <Upload className="mr-2 h-4 w-4" />
                                  Choose File
                                </Button>
                              </label>
                            </>
                          )}
                          <p className="text-xs text-muted-foreground mt-2">
                            JPG, PNG up to 5MB • No face data is stored
                          </p>
                        </div>
                      ) : (
                        <div className="text-center">
                          <Image
                            src={URL.createObjectURL(selectedFile)}
                            alt="Selected"
                            width={200}
                            height={200}
                            className="mx-auto rounded-lg mb-4 object-cover"
                          />
                          <p className="font-medium mb-2">{selectedFile.name}</p>
                          <Button variant="outline" onClick={removeFile} size="sm">
                            Remove
                          </Button>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </>
        ) : (
          <div className="col-span-2">
            <AttractivenessPieChart data={result} />
            <div className="text-center mt-6">
              <Button onClick={() => setResult(null)} variant="outline">
                Analyze Another Photo
              </Button>
            </div>
          </div>
        )}
      </div>
    </section>
  );
}
