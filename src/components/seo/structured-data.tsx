import Script from 'next/script';

interface StructuredDataProps {
  type?: 'website' | 'webapp' | 'article';
}

export default function StructuredData({ type = 'webapp' }: StructuredDataProps) {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://attractive-score.vercel.app';
  
  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "AttractivenessScore",
    "alternateName": "How Attractive Am I",
    "url": baseUrl,
    "description": "AI-powered facial analysis and attractiveness scoring tool with detailed insights and personalized recommendations.",
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": `${baseUrl}/?q={search_term_string}`
      },
      "query-input": "required name=search_term_string"
    },
    "publisher": {
      "@type": "Organization",
      "name": "AttractivenessScore",
      "url": baseUrl,
      "logo": {
        "@type": "ImageObject",
        "url": `${baseUrl}/logo.png`,
        "width": 512,
        "height": 512
      }
    }
  };

  const webAppSchema = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "AttractivenessScore - AI Facial Analysis",
    "alternateName": "How Attractive Am I",
    "url": baseUrl,
    "description": "Advanced AI-powered facial analysis tool that provides detailed attractiveness scoring, face shape detection, and golden ratio analysis with personalized beauty recommendations.",
    "applicationCategory": "UtilityApplication",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD",
      "availability": "https://schema.org/InStock"
    },
    "featureList": [
      "AI-powered attractiveness scoring",
      "Facial proportions analysis",
      "Face shape detection",
      "Golden ratio analysis",
      "Personalized beauty recommendations",
      "Instant results",
      "Privacy-focused (no data storage)"
    ],
    "screenshot": {
      "@type": "ImageObject",
      "url": `${baseUrl}/screenshot.jpg`,
      "width": 1200,
      "height": 800
    },
    "publisher": {
      "@type": "Organization",
      "name": "AttractivenessScore",
      "url": baseUrl
    },
    "author": {
      "@type": "Organization",
      "name": "AttractivenessScore Team"
    },
    "datePublished": "2024-01-01",
    "dateModified": new Date().toISOString().split('T')[0],
    "inLanguage": "en-US",
    "isAccessibleForFree": true,
    "usageInfo": `${baseUrl}/terms-of-service`,
    "privacyPolicy": `${baseUrl}/privacy-policy`
  };

  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "AttractivenessScore",
    "url": baseUrl,
    "logo": {
      "@type": "ImageObject",
      "url": `${baseUrl}/logo.png`,
      "width": 512,
      "height": 512
    },
    "description": "Leading provider of AI-powered facial analysis and beauty assessment tools.",
    "foundingDate": "2024",
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "availableLanguage": "English"
    },
    "sameAs": [
      "https://twitter.com/attractivescore"
    ]
  };

  const faqSchema = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": "How does the AI attractiveness analysis work?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Our AI analyzes facial features including proportions, symmetry, skin quality, and overall harmony using advanced computer vision algorithms. The analysis is based on established beauty standards and provides detailed scoring across multiple factors."
        }
      },
      {
        "@type": "Question",
        "name": "Is my photo stored or shared?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "No, we prioritize your privacy. Photos are processed in real-time and are not stored on our servers. The analysis is performed instantly and your image data is immediately discarded after processing."
        }
      },
      {
        "@type": "Question",
        "name": "What types of analysis are available?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "We offer multiple analysis types including attractiveness scoring, face shape detection, and golden ratio analysis. Each provides unique insights and personalized recommendations for enhancement."
        }
      },
      {
        "@type": "Question",
        "name": "Is the service free to use?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Yes, our basic facial analysis service is completely free to use. Simply upload your photo and receive instant AI-powered insights about your facial features and attractiveness score."
        }
      }
    ]
  };

  const schemas = [websiteSchema, organizationSchema, faqSchema];
  
  if (type === 'webapp') {
    schemas.push(webAppSchema);
  }

  return (
    <>
      {schemas.map((schema, index) => (
        <Script
          key={index}
          id={`structured-data-${index}`}
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(schema),
          }}
        />
      ))}
    </>
  );
}
