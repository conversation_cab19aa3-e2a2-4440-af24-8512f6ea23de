import type { <PERSON>ada<PERSON> } from "next";
import { Poppins } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "../components/theme/theme-provider";
import { Clerk<PERSON>rovider } from "@clerk/nextjs";
import MediavineAdScript from "../components/MediavineAdScript";
import GrowMeScript from "../components/GrowMeScript";
import UmamiAnalytics from "../components/UmamiAnalytics";

const poppins = Poppins({
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "AttractivenessScore",
  description: "AI-powered facial analysis and scoring",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head />
      <body suppressHydrationWarning className={poppins.className}>
        <ClerkProvider publishableKey={process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY as string}>
          <ThemeProvider>
            {children}
            <MediavineAdScript />
            <GrowMeScript />
            <UmamiAnalytics />
          </ThemeProvider>
        </ClerkProvider>
      </body>
    </html>
  );
}
