import { NextRequest, NextResponse } from "next/server";
import { getAuth } from "@clerk/nextjs/server";
import { PROMPTS } from '../../../lib/prompts';
// Function to check if user is paid (implement your logic here)
async function isUserPaid(userId: string): Promise<boolean> {
  // TODO: Implement your paid user check logic
  // This could involve checking Clerk metadata, a subscription database, etc.
  return true; // Temporary: return true for testing
}

// Function to extract JSO<PERSON> from markdown code blocks if present
function extractJsonFromMarkdown(content: string): string {
  // Try to extract JSON from markdown code blocks
  const jsonMatch = content.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
  if (jsonMatch && jsonMatch[1]) {
    return jsonMatch[1];
  }
  // If no markdown blocks found, return the original content
  return content;
}

export async function POST(req: NextRequest) {
  try {
    // Removed auth check for free analysis

    const formData = await req.formData();
    const image = formData.get("image") as File;
    const analysisTypeRaw = formData.get("analysisType") as string || "attractive-score";

    // Parse analysis types - could be string or string[]
    let analysisTypes: string[];
    try {
      const parsed = JSON.parse(analysisTypeRaw);
      analysisTypes = Array.isArray(parsed) ? parsed : [parsed];
    } catch {
      analysisTypes = [analysisTypeRaw];
    }

    // Create combined prompt if multiple types selected
    let combinedPrompt = "";
    if (analysisTypes.length > 1) {
      combinedPrompt = `First, verify this is a clear photo of a human face. Look for:
  - Human facial features (eyes, nose, mouth)
  - Natural skin texture
  - Clear facial structure
  - Recognizable human face

  If ANY of these are missing or if the image shows non-human subjects (animals, objects, artwork, etc.), respond with this exact JSON:
  {
    "error": "Please upload a human face to begin"
  }

  If a human face is detected, analyze the image comprehensively covering ALL requested analyses: ${analysisTypes.join(', ')}.

  For EACH analysis type, provide detailed and specific analysis using the format below. Cover all requested types (${analysisTypes.join(', ')}) in your comprehensive response.

  IMPORTANT RULES across ALL analyses:
  1. Overall score must include one decimal place (e.g., 7.8, 6.4, not whole numbers)
  2. Use "you" and "your" in descriptions and recommendations (not "the subject" or "they")
  3. Only suggest natural improvements (NO surgery, professional consultations, or medical procedures)
  4. Keep recommendations practical and achievable
  5. Maintain a positive, encouraging tone
  6. All scores must be between 5 and 10 (no scores below 5)
  7. Make recommendations specific to what you observe in the image
  8. Highlight existing strengths while suggesting subtle enhancements

  Return COMPREHENSIVE JSON array with results for each requested analysis type:
  {
    "analyses": [
      {
        "type": "attractive-score",
        "overall_score": number (decimal),
        "factors": [
          { "name": "Facial Proportions", "score": number, "description": string, "recommendation": string },
          { "name": "Skin Quality", "score": number, "description": string, "recommendation": string },
          { "name": "Facial Features Harmony", "score": number, "description": string, "recommendation": string },
          { "name": "Facial Structure", "score": number, "description": string, "recommendation": string },
          { "name": "Expression Quality", "score": number, "description": string, "recommendation": string }
        ],
        "analysis_text": string,
        "overall_recommendations": string
      },
      {
        "type": "face-shape-detector",
        "shape": string,
        "confidence_score": number (0-1),
        "measurements": { /* face shape measurements */ },
        "description": string,
        "styling_tips": [ /* styling recommendations */ ],
        "celebrity_examples": string[],
        "overall_score": number,
        "analysis_text": string
      }
    ]
  }

  The response must include ALL requested analysis types: ${analysisTypes.join(', ')}.`;
    } else {
      combinedPrompt = PROMPTS.attractiveScore; // fallback to default
    }

    // Select prompt based on analysis types
    let selectedPrompt = combinedPrompt; // Use combined prompt if multiple types
    if (analysisTypes.length === 1) {
      const promptMap = {
        "attractive-score": PROMPTS.attractiveScore,
        "smile-rating": PROMPTS.smileAnalysis,
        "face-shape-detector": PROMPTS.faceShapeDetector,
        "golden-ratio-face": PROMPTS.attractiveScore
      };
      selectedPrompt = promptMap[analysisTypes[0]] || PROMPTS.attractiveScore;
    }

    if (!image) {
      console.log('No image provided in request');
      return NextResponse.json({ error: "No image provided" }, { status: 400 });
    }

    console.log('Image received:', {
      name: image.name,
      type: image.type,
      size: image.size
    });

    // Convert image to base64
    const bytes = await image.arrayBuffer();
    const base64Image = Buffer.from(bytes).toString('base64');
    const imageDataUrl = `data:${image.type};base64,${base64Image}`;

    // Check if OpenRouter API key is configured
    const openRouterApiKey = process.env.OPENROUTER_API_KEY;
    if (!openRouterApiKey) {
      console.error('OpenRouter API key not configured');
      return NextResponse.json({ error: "Service misconfigured" }, { status: 500 });
    }

    // Call OpenRouter Vision AI API
    console.log('Sending request to OpenRouter...');
    try {
      const headers = new Headers();
      headers.append('Authorization', `Bearer ${openRouterApiKey}`);
      headers.append('HTTP-Referer', process.env.OPENROUTER_REFERER || 'https://attractive-score.vercel.app');
      headers.append('X-Title', process.env.OPENROUTER_TITLE || 'AI Attractiveness Scale');
      headers.append('Content-Type', 'application/json');

      const validAnalysisTypes = ["attractive-score", "smile-rating", "face-shape-detector", "golden-ratio-face"] as const;
      type AnalysisType = typeof validAnalysisTypes[number];

      // Validate all requested analysis types
      for (const type of analysisTypes) {
        if (!validAnalysisTypes.includes(type as AnalysisType)) {
          return NextResponse.json({ error: "Invalid analysis type" }, { status: 400 });
        }
      }

      const openRouterResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: headers,
        body: JSON.stringify({
          model: 'google/gemini-2.5-flash-lite',
          messages: [
            {
              role: 'user',
              content: [
                {
                  type: 'text',
                  text: selectedPrompt
                },
                {
                  type: 'image_url',
                  image_url: imageDataUrl
                }
              ]
            }
          ]
        })
      });

      if (!openRouterResponse.ok) {
        const responseText = await openRouterResponse.text();
        console.error('OpenRouter API error:', responseText);
        return NextResponse.json({ 
          error: 'Failed to analyze image',
          details: responseText 
        }, { status: openRouterResponse.status });
      }

      const aiResponse = await openRouterResponse.json();

      if (!aiResponse.choices?.[0]?.message?.content) {
        console.error('Unexpected API response format:', aiResponse);
        return NextResponse.json({ 
          error: 'Invalid API response format',
          details: aiResponse 
        }, { status: 500 });
      }

      const content = aiResponse.choices[0].message.content;
      console.log('Raw AI response:', content);

      // Check if the response indicates no face was found
      if (content.includes("The image doesn't show a face") || 
          content.includes("No facial structure is visible") ||
          content.includes("The image does not show any facial features")) {
        return NextResponse.json({ 
          error: 'No human face detected in the image. Please upload a clear photo of a face.'
        }, { status: 400 });
      }


      let analysisResult;
      try {
        // Try to parse the content directly first
        try {
          analysisResult = JSON.parse(content);
        } catch {
          // If direct parsing fails, try to extract JSON from markdown
          const extractedJson = extractJsonFromMarkdown(content);
          analysisResult = JSON.parse(extractedJson);
        }

        // Check if the AI detected no human face
        if (analysisResult.error) {
          return NextResponse.json({ 
            error: analysisResult.error
          }, { status: 400 });
        }

      } catch (error) {
        console.error('Failed to parse AI response content:', error);
        return NextResponse.json({ 
          error: 'Failed to parse analysis result',
          details: content
        }, { status: 500 });
      }

      // TODO: Implement cloud storage for paid users
      console.log('Analysis complete');

      return NextResponse.json(analysisResult);
    } catch (error) {
      console.error("OpenRouter API error:", error);
      return NextResponse.json({ 
        error: "Failed to connect to OpenRouter API",
        details: error instanceof Error ? error.message : String(error)
      }, { status: 500 });
    }
  } catch (error) {
    console.error("Server error:", error);
    return NextResponse.json({ 
      error: "Internal server error",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
